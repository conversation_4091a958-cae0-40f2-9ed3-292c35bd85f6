
'use client';

import { Header } from '@/components/Header';

export default function Home() {
  const handleDonateClick = () => {
    console.log('Donate button clicked');
    // Add your donation logic here
  };

  const handleJoinClick = () => {
    console.log('Join Now button clicked');
    // Add your join logic here
  };

  const handleLogoClick = () => {
    console.log('Logo clicked');
    // Add your logo click logic here (e.g., navigate to home)
  };

  return (
    <div className="min-h-screen">
      <Header
        onDonateClick={handleDonateClick}
        onJoinClick={handleJoinClick}
        onLogoClick={handleLogoClick}
      />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to Invest Founders
          </h1>
          <p className="text-lg text-gray-600">
            Your investment platform developed by Kriptaz Blockchain
          </p>
        </div>
      </main>
    </div>
  );
}
