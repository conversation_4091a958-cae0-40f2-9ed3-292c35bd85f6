/* Hero Component Styles */

.hero {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--font-primary);
}

.backgroundSection {
  position: relative;
  flex: 1;
  min-height: 65vh;
  background-color: #f0f0f0;
  background-image: linear-gradient(
    135deg,
    #e0e0e0 0%,
    #f0f0f0 50%,
    #e0e0e0 100%
  );
  background-size: cover;
  background-position: center bottom;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .backgroundSection {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .backgroundSection {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.videoBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  background-color: #000;
}



.backgroundOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 5;
  pointer-events: none;
}

.videoOverlayImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  z-index: 2;
  opacity: 0.3;
  mix-blend-mode: overlay;
  pointer-events: none;
}

/* SVG-specific styling prioritizing complete content visibility */
.videoOverlayImage[src$=".svg"] {
  object-fit: contain;
  object-position: center;
  /* Ensure complete SVG visibility without forced fitting */
  width: 100%;
  height: 100%;
  /* Remove scaling to preserve natural SVG proportions */
  transform: none;
  /* Allow natural SVG display without distortion */
}

.videoFilters {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.05) 20%,
    rgba(0, 0, 0, 0.02) 50%,
    rgba(0, 0, 0, 0.05) 80%,
    rgba(0, 0, 0, 0.2) 100%
  );
  z-index: 3;
  pointer-events: none;
}

.contentSection {
  background-color: var(--color-light-gray);
  padding: 4rem 1rem 5rem 1rem;
  min-height: 35vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 10;
  width: 100%;
  position: relative;
}

@media (min-width: 640px) {
  .contentSection {
    padding: 4rem 1.5rem 5rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .contentSection {
    padding: 4rem 2rem 5rem 2rem;
  }
}

/* Horizontal line above content section */
.contentSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-primary-black);
  opacity: 0.2;
  z-index: 1;
}

/* Horizontal line below content section */
.contentSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-primary-black);
  opacity: 0.2;
  z-index: 1;
}

.content {
  max-width: 1200px;
  width: 100%;
  padding: 0 2rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.15;
  color: var(--color-primary-black);
  margin-bottom: 3rem;
  font-family: var(--font-primary);
  letter-spacing: -0.02em;
  text-align: left;
  max-width: 100%;
}

.textContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-top: 0;
  width: 100%;
  align-items: start;
  justify-content: space-between;
}

.textColumn {
  font-size: 1rem;
  line-height: 1.7;
  color: var(--color-primary-black);
  text-align: justify;
  font-family: var(--font-primary);
  text-justify: inter-word;
  word-spacing: 0.05em;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .backgroundSection {
    min-height: 60vh;
  }

  .contentSection {
    min-height: 40vh;
    padding: 3rem 1.5rem 4rem 1.5rem;
  }

  .content {
    max-width: 1000px;
    padding: 0 1.5rem;
  }

  .title {
    font-size: 2.25rem;
    margin-bottom: 2.5rem;
  }

  .textContainer {
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: 100vh;
  }

  .backgroundSection {
    min-height: 55vh;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .contentSection {
    min-height: 45vh;
    padding: 2.5rem 1rem 3.5rem 1rem;
  }

  .content {
    max-width: 100%;
    padding: 0 1rem;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: left;
    line-height: 1.2;
  }

  .textContainer {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 0;
  }

  .textColumn {
    font-size: 0.95rem;
    line-height: 1.6;
    text-align: justify;
    text-justify: inter-word;
    word-spacing: 0.05em;
  }
}

@media (max-width: 480px) {
  .backgroundSection {
    min-height: 50vh;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .contentSection {
    min-height: 50vh;
    padding: 2rem 0.75rem 3rem 0.75rem;
  }

  .content {
    padding: 0 0.5rem;
  }

  .title {
    font-size: 1.75rem;
    line-height: 1.25;
    margin-bottom: 1.5rem;
  }

  .textContainer {
    gap: 1.5rem;
  }

  .textColumn {
    font-size: 0.9rem;
    line-height: 1.6;
    text-justify: inter-word;
    word-spacing: 0.05em;
  }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .videoBackground {
    display: none;
  }

  .backgroundSection {
    background-image: linear-gradient(
      135deg,
      #f0f0f0 0%,
      #e0e0e0 50%,
      #f0f0f0 100%
    );
  }
}

/* Focus states for accessibility */
.hero:focus-within {
  outline: 2px solid var(--color-accent-green);
  outline-offset: 4px;
}

/* Ensure proper contrast and readability */
