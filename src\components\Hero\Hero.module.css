/* Hero Component Styles */

.hero {
  position: relative;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--font-primary);
}

.backgroundSection {
  position: relative;
  flex: 1;
  min-height: 65vh;
  background-color: #f0f0f0;
  background-image: linear-gradient(
    135deg,
    #e0e0e0 0%,
    #f0f0f0 50%,
    #e0e0e0 100%
  );
  background-size: cover;
  background-position: center bottom;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  background-color: #000;
}



.backgroundOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 5;
  pointer-events: none;
}

.videoOverlayImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  z-index: 2;
  opacity: 0.3;
  mix-blend-mode: overlay;
  pointer-events: none;
}

/* SVG-specific styling prioritizing complete content visibility */
.videoOverlayImage[src$=".svg"] {
  object-fit: contain;
  object-position: center;
  /* Ensure complete SVG visibility without forced fitting */
  width: 100%;
  height: 100%;
  /* Remove scaling to preserve natural SVG proportions */
  transform: none;
  /* Allow natural SVG display without distortion */
}

.videoFilters {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.05) 20%,
    rgba(0, 0, 0, 0.02) 50%,
    rgba(0, 0, 0, 0.05) 80%,
    rgba(0, 0, 0, 0.2) 100%
  );
  z-index: 3;
  pointer-events: none;
}

.contentSection {
  background-color: var(--color-light-gray);
  padding: 3rem 0;
  min-height: 35vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.content {
  max-width: 1200px;
  width: 100%;
  padding: 0 1rem;
}

.title {
  font-size: 2.75rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-primary-black);
  margin-bottom: 2rem;
  font-family: var(--font-primary);
  letter-spacing: -0.01em;
  text-align: justify;
  /* Enhanced text distribution for better space utilization */
  text-justify: inter-word;
  word-spacing: 0.05em;
  hyphens: auto;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
}

.textContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 1.5rem;
  /* Ensure full width utilization and proper alignment */
  width: 100%;
  align-items: start;
  justify-content: space-between;
}

.textColumn {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--color-primary-black);
  text-align: justify;
  font-family: var(--font-primary);
  /* Enhanced text distribution for better space utilization */
  text-justify: inter-word;
  word-spacing: 0.1em;
  hyphens: auto;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .backgroundSection {
    min-height: 60vh;
  }

  .contentSection {
    min-height: 40vh;
    padding: 2.5rem 0;
  }

  .content {
    padding: 0 1rem;
  }

  .title {
    font-size: 2.5rem;
  }

  .textContainer {
    gap: 2.5rem;
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: 100vh;
  }

  .backgroundSection {
    min-height: 55vh;
  }

  .contentSection {
    min-height: 45vh;
    padding: 2rem 0;
  }

  .content {
    padding: 0 0.75rem;
  }

  .title {
    font-size: 2.25rem;
    margin-bottom: 1.5rem;
    text-align: justify;
    /* Enhanced mobile title text distribution */
    text-justify: inter-word;
    word-spacing: 0.04em;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
  }

  .textContainer {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 1rem;
  }

  .textColumn {
    font-size: 0.95rem;
    text-align: justify;
    /* Enhanced mobile text distribution */
    text-justify: inter-word;
    word-spacing: 0.08em;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
  }
}

@media (max-width: 480px) {
  .backgroundSection {
    min-height: 50vh;
  }

  .contentSection {
    min-height: 50vh;
  }

  .title {
    font-size: 1.875rem;
    line-height: 1.3;
  }

  .textColumn {
    font-size: 0.9rem;
    /* Enhanced small screen text distribution */
    text-justify: inter-word;
    word-spacing: 0.06em;
    hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
  }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .videoBackground {
    display: none;
  }

  .backgroundSection {
    background-image: linear-gradient(
      135deg,
      #f0f0f0 0%,
      #e0e0e0 50%,
      #f0f0f0 100%
    );
  }
}

/* Focus states for accessibility */
.hero:focus-within {
  outline: 2px solid var(--color-accent-green);
  outline-offset: 4px;
}

/* Ensure proper contrast and readability */
.contentSection {
  position: relative;
  z-index: 10;
}
