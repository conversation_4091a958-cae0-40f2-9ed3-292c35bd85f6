'use client';

import React, { useRef, useEffect, useState } from 'react';
import { HeroProps } from '@/types/hero';
import styles from './Hero.module.css';

/**
 * Hero component with video background
 * Displays main heading and two-column descriptive text
 * Responsive design with accessibility features
 */
const Hero: React.FC<HeroProps> = ({
  className = '',
  videoSrc = '/video/hero-video.mp4',
  backgroundImage,
  videoOverlayImage = '/images/video-filter.webp',
  title = 'Build Your Noble Legacy for Generations to Come',
  leftText = 'Grand Founders is an economic development and national security foundation that makes impact investments and supports emerging entrepreneurs globally, ensuring a noble legacy for generations to come.',
  rightText = 'Grand Founders is a noble community of mission-driven and socially responsible multimillionaires, united by similar culture and values. The foundation is NYC based 501c3, and fully backed and supervised by Grand Founders Ambassadors.',
  onVideoLoad,
  onVideoError
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [videoError, setVideoError] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedData = () => {
      setVideoLoaded(true);
      onVideoLoad?.();
    };

    const handleError = (error: Event) => {
      setVideoError(true);
      onVideoError?.(error);
      console.warn('Hero video failed to load:', error);
    };

    const handleCanPlay = () => {
      // Ensure video plays when ready
      video.play().catch((error) => {
        console.warn('Video autoplay failed:', error);
      });
    };

    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('error', handleError);
    video.addEventListener('canplay', handleCanPlay);

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('error', handleError);
      video.removeEventListener('canplay', handleCanPlay);
    };
  }, [onVideoLoad, onVideoError]);

  return (
    <section
      className={`${styles.hero} ${className}`}
      role="banner"
      aria-label="Hero section with company mission"
    >
      {/* Background Section with Video */}
      <div
        className={styles.backgroundSection}
        style={{
          backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined
        }}
      >
        {!videoError && (
          <video
            ref={videoRef}
            className={styles.videoBackground}
            autoPlay
            muted
            loop
            playsInline
            preload="metadata"
            aria-hidden="true"
          >
            <source src={videoSrc} type="video/mp4" />
            {/* Fallback for browsers that don't support video */}
            Your browser does not support the video tag.
          </video>
        )}

        {/* Video Overlay Image */}
        <img
          src={videoOverlayImage}
          alt=""
          className={styles.videoOverlayImage}
          aria-hidden="true"
        />

        {/* Video Filters */}
        <div className={styles.videoFilters} aria-hidden="true" />

        <div className={styles.backgroundOverlay} aria-hidden="true" />
      </div>

      {/* Content Section */}
      <div className={styles.contentSection}>
        <div className={styles.content}>
          <h1 className={styles.title}>
            {title}
          </h1>

          <div className={styles.textContainer}>
            <div className={styles.textColumn}>
              <p>{leftText}</p>
            </div>
            <div className={styles.textColumn}>
              <p>{rightText}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
