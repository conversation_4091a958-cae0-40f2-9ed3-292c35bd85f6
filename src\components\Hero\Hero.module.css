/* Hero Component Styles */

.hero {
  position: relative;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--color-light-gray);
  font-family: var(--font-primary);
}

.videoBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  opacity: 0.8;
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(231, 231, 231, 0.9) 0%,
    rgba(231, 231, 231, 0.7) 50%,
    rgba(231, 231, 231, 0.9) 100%
  );
  z-index: 2;
}

.content {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  width: 100%;
  padding: 0 2rem;
  text-align: center;
}

.title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  color: var(--color-primary-black);
  margin-bottom: 3rem;
  font-family: var(--font-primary);
  letter-spacing: -0.02em;
}

.textContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-top: 3rem;
}

.textColumn {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--color-primary-black);
  text-align: left;
  font-family: var(--font-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content {
    padding: 0 1.5rem;
  }
  
  .title {
    font-size: 3rem;
  }
  
  .textContainer {
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: 80vh;
  }
  
  .content {
    padding: 0 1rem;
  }
  
  .title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }
  
  .textContainer {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
  }
  
  .textColumn {
    font-size: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .textColumn {
    font-size: 0.95rem;
  }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .videoBackground {
    display: none;
  }
  
  .hero {
    background-image: linear-gradient(
      135deg,
      var(--color-light-gray) 0%,
      #f5f5f5 50%,
      var(--color-light-gray) 100%
    );
  }
  
  .videoOverlay {
    background: rgba(231, 231, 231, 0.1);
  }
}

/* Focus states for accessibility */
.hero:focus-within {
  outline: 2px solid var(--color-accent-green);
  outline-offset: 4px;
}
